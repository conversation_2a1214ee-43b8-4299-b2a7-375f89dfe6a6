function(){return (function(){var h=this||self;function aa(a){return"string"==typeof a}function ba(a,b){a=a.split(".");var c=h;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b}
function ca(a){var b=typeof a;if("object"==b)if(a){if(a instanceof Array)return"array";if(a instanceof Object)return b;var c=Object.prototype.toString.call(a);if("[object Window]"==c)return"object";if("[object Array]"==c||"number"==typeof a.length&&"undefined"!=typeof a.splice&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("splice"))return"array";if("[object Function]"==c||"undefined"!=typeof a.call&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("call"))return"function"}else return"null";
else if("function"==b&&"undefined"==typeof a.call)return"object";return b}function da(a){var b=typeof a;return"object"==b&&null!=a||"function"==b}function ea(a,b,c){return a.call.apply(a.bind,arguments)}function fa(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}
function ha(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?ha=ea:ha=fa;return ha.apply(null,arguments)}function ia(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function m(a,b){function c(){}c.prototype=b.prototype;a.H=b.prototype;a.prototype=new c;a.prototype.constructor=a}
function ja(a,b,c){var d=arguments.callee.caller;if("undefined"!==typeof d.H){for(var e=Array(arguments.length-1),f=1;f<arguments.length;f++)e[f-1]=arguments[f];d.H.constructor.apply(a,e)}else{if("string"!=typeof b&&"symbol"!=typeof b)throw Error("method names provided to goog.base must be a string or a symbol");e=Array(arguments.length-2);for(f=2;f<arguments.length;f++)e[f-2]=arguments[f];f=!1;for(var g=a.constructor.prototype;g;g=Object.getPrototypeOf(g))if(g[b]===d)f=!0;else if(f){g[b].apply(a,
e);return}if(a[b]===d)a.constructor.prototype[b].apply(a,e);else throw Error("goog.base called from a method of one name to a method of a different name");}};/*

 The MIT License

 Copyright (c) 2007 Cybozu Labs, Inc.
 Copyright (c) 2012 Google Inc.

 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to
 deal in the Software without restriction, including without limitation the
 rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 sell copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 IN THE SOFTWARE.
*/
function ka(a,b,c){this.a=a;this.b=b||1;this.f=c||1};var la;var ma=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},n=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)},na=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,
b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f="string"===typeof a?a.split(""):a,g=0;g<c;g++)if(g in f){var k=f[g];b.call(void 0,k,g,a)&&(d[e++]=k)}return d},oa=Array.prototype.map?function(a,b){return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e="string"===typeof a?a.split(""):a,f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d},pa=Array.prototype.reduce?function(a,b,c){return Array.prototype.reduce.call(a,b,c)}:function(a,b,c){var d=c;n(a,
function(e,f){d=b.call(void 0,d,e,f,a)});return d},qa=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};function ra(a,b){a:{for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a)){b=e;break a}b=-1}return 0>b?null:"string"===typeof a?a.charAt(b):a[b]}
function sa(a){return Array.prototype.concat.apply([],arguments)}function ta(a,b,c){return 2>=arguments.length?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};var ua=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};
function va(a,b){var c=0;a=ua(String(a)).split(".");b=ua(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;0==c&&e<d;e++){var f=a[e]||"",g=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(0==f[0].length&&0==g[0].length)break;c=wa(0==f[1].length?0:parseInt(f[1],10),0==g[1].length?0:parseInt(g[1],10))||wa(0==f[2].length,0==g[2].length)||wa(f[2],g[2]);f=f[3];g=g[3]}while(0==c)}return c}function wa(a,b){return a<b?-1:a>b?1:0};var p;a:{var xa=h.navigator;if(xa){var ya=xa.userAgent;if(ya){p=ya;break a}}p=""}function q(a){return-1!=p.indexOf(a)};function za(){return q("Firefox")||q("FxiOS")}function Aa(){return(q("Chrome")||q("CriOS"))&&!q("Edge")};function Ba(){return q("iPhone")&&!q("iPod")&&!q("iPad")};function Ca(a,b){var c=Da;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};var Ea=q("Opera"),r=q("Trident")||q("MSIE"),Fa=q("Edge"),t=q("Gecko")&&!(-1!=p.toLowerCase().indexOf("webkit")&&!q("Edge"))&&!(q("Trident")||q("MSIE"))&&!q("Edge"),Ga=-1!=p.toLowerCase().indexOf("webkit")&&!q("Edge"),Ha=q("Macintosh"),Ia=q("Windows");function Ja(){var a=h.document;return a?a.documentMode:void 0}var Ka;
a:{var La="",Ma=function(){var a=p;if(t)return/rv:([^\);]+)(\)|;)/.exec(a);if(Fa)return/Edge\/([\d\.]+)/.exec(a);if(r)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(Ga)return/WebKit\/(\S+)/.exec(a);if(Ea)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Ma&&(La=Ma?Ma[1]:"");if(r){var Na=Ja();if(null!=Na&&Na>parseFloat(La)){Ka=String(Na);break a}}Ka=La}var Da={};function Oa(a){return Ca(a,function(){return 0<=va(Ka,a)})}var w;w=h.document&&r?Ja():void 0;var x=r&&!(9<=Number(w)),Pa=r&&!(8<=Number(w));function Qa(a,b,c,d){this.a=a;this.nodeName=c;this.nodeValue=d;this.nodeType=2;this.parentNode=this.ownerElement=b}function Sa(a,b){var c=Pa&&"href"==b.nodeName?a.getAttribute(b.nodeName,2):b.nodeValue;return new Qa(b,a,b.nodeName,c)};function Ta(a){this.b=a;this.a=0}function Ua(a){a=a.match(Va);for(var b=0;b<a.length;b++)Wa.test(a[b])&&a.splice(b,1);return new Ta(a)}var Va=/\$?(?:(?![0-9-\.])(?:\*|[\w-\.]+):)?(?![0-9-\.])(?:\*|[\w-\.]+)|\/\/|\.\.|::|\d+(?:\.\d*)?|\.\d+|"[^"]*"|'[^']*'|[!<>]=|\s+|./g,Wa=/^\s/;function y(a,b){return a.b[a.a+(b||0)]}function z(a){return a.b[a.a++]}function Xa(a){return a.b.length<=a.a};function Ya(a){return a.scrollingElement?a.scrollingElement:Ga||"CSS1Compat"!=a.compatMode?a.body||a.documentElement:a.documentElement}function Za(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a}
function $a(a,b){if(a==b)return 0;if(a.compareDocumentPosition)return a.compareDocumentPosition(b)&2?1:-1;if(r&&!(9<=Number(w))){if(9==a.nodeType)return-1;if(9==b.nodeType)return 1}if("sourceIndex"in a||a.parentNode&&"sourceIndex"in a.parentNode){var c=1==a.nodeType,d=1==b.nodeType;if(c&&d)return a.sourceIndex-b.sourceIndex;var e=a.parentNode,f=b.parentNode;return e==f?ab(a,b):!c&&Za(e,b)?-1*bb(a,b):!d&&Za(f,a)?bb(b,a):(c?a.sourceIndex:e.sourceIndex)-(d?b.sourceIndex:f.sourceIndex)}d=A(a);c=d.createRange();
c.selectNode(a);c.collapse(!0);a=d.createRange();a.selectNode(b);a.collapse(!0);return c.compareBoundaryPoints(h.Range.START_TO_END,a)}function bb(a,b){var c=a.parentNode;if(c==b)return-1;for(;b.parentNode!=c;)b=b.parentNode;return ab(b,a)}function ab(a,b){for(;b=b.previousSibling;)if(b==a)return-1;return 1}function A(a){return 9==a.nodeType?a:a.ownerDocument||a.document}var cb={SCRIPT:1,STYLE:1,HEAD:1,IFRAME:1,OBJECT:1},db={IMG:" ",BR:"\n"};
function eb(a,b,c){if(!(a.nodeName in cb))if(3==a.nodeType)c?b.push(String(a.nodeValue).replace(/(\r\n|\r|\n)/g,"")):b.push(a.nodeValue);else if(a.nodeName in db)b.push(db[a.nodeName]);else for(a=a.firstChild;a;)eb(a,b,c),a=a.nextSibling}function fb(a){this.a=a||h.document||document}fb.prototype.getElementsByTagName=function(a,b){return(b||this.a).getElementsByTagName(String(a))};function B(a){var b=null,c=a.nodeType;1==c&&(b=a.textContent,b=void 0==b||null==b?a.innerText:b,b=void 0==b||null==b?"":b);if("string"!=typeof b)if(x&&"title"==a.nodeName.toLowerCase()&&1==c)b=a.text;else if(9==c||1==c){a=9==c?a.documentElement:a.firstChild;c=0;var d=[];for(b="";a;){do 1!=a.nodeType&&(b+=a.nodeValue),x&&"title"==a.nodeName.toLowerCase()&&(b+=a.text),d[c++]=a;while(a=a.firstChild);for(;c&&!(a=d[--c].nextSibling););}}else b=a.nodeValue;return b}
function C(a,b,c){if(null===b)return!0;try{if(!a.getAttribute)return!1}catch(d){return!1}Pa&&"class"==b&&(b="className");return null==c?!!a.getAttribute(b):a.getAttribute(b,2)==c}function gb(a,b,c,d,e){return(x?hb:ib).call(null,a,b,aa(c)?c:null,aa(d)?d:null,e||new D)}
function hb(a,b,c,d,e){if(a instanceof jb||8==a.b||c&&null===a.b){var f=b.all;if(!f)return e;a=kb(a);if("*"!=a&&(f=b.getElementsByTagName(a),!f))return e;if(c){for(var g=[],k=0;b=f[k++];)C(b,c,d)&&g.push(b);f=g}for(k=0;b=f[k++];)"*"==a&&"!"==b.tagName||e.add(b);return e}lb(a,b,c,d,e);return e}
function ib(a,b,c,d,e){b.getElementsByName&&d&&"name"==c&&!r?(b=b.getElementsByName(d),n(b,function(f){a.a(f)&&e.add(f)})):b.getElementsByClassName&&d&&"class"==c?(b=b.getElementsByClassName(d),n(b,function(f){f.className==d&&a.a(f)&&e.add(f)})):a instanceof E?lb(a,b,c,d,e):b.getElementsByTagName&&(b=b.getElementsByTagName(a.f()),n(b,function(f){C(f,c,d)&&e.add(f)}));return e}
function mb(a,b,c,d,e){var f;if((a instanceof jb||8==a.b||c&&null===a.b)&&(f=b.childNodes)){var g=kb(a);if("*"!=g&&(f=na(f,function(k){return k.tagName&&k.tagName.toLowerCase()==g}),!f))return e;c&&(f=na(f,function(k){return C(k,c,d)}));n(f,function(k){"*"==g&&("!"==k.tagName||"*"==g&&1!=k.nodeType)||e.add(k)});return e}return nb(a,b,c,d,e)}function nb(a,b,c,d,e){for(b=b.firstChild;b;b=b.nextSibling)C(b,c,d)&&a.a(b)&&e.add(b);return e}
function lb(a,b,c,d,e){for(b=b.firstChild;b;b=b.nextSibling)C(b,c,d)&&a.a(b)&&e.add(b),lb(a,b,c,d,e)}function kb(a){if(a instanceof E){if(8==a.b)return"!";if(null===a.b)return"*"}return a.f()};function D(){this.b=this.a=null;this.o=0}function ob(a){this.f=a;this.a=this.b=null}function pb(a,b){if(!a.a)return b;if(!b.a)return a;var c=a.a;b=b.a;for(var d=null,e,f=0;c&&b;){e=c.f;var g=b.f;e==g||e instanceof Qa&&g instanceof Qa&&e.a==g.a?(e=c,c=c.a,b=b.a):0<$a(c.f,b.f)?(e=b,b=b.a):(e=c,c=c.a);(e.b=d)?d.a=e:a.a=e;d=e;f++}for(e=c||b;e;)e.b=d,d=d.a=e,f++,e=e.a;a.b=d;a.o=f;return a}function qb(a,b){b=new ob(b);b.a=a.a;a.b?a.a.b=b:a.a=a.b=b;a.a=b;a.o++}
D.prototype.add=function(a){a=new ob(a);a.b=this.b;this.a?this.b.a=a:this.a=this.b=a;this.b=a;this.o++};function rb(a){return(a=a.a)?a.f:null}function sb(a){return(a=rb(a))?B(a):""}function F(a,b){return new tb(a,!!b)}function tb(a,b){this.f=a;this.b=(this.v=b)?a.b:a.a;this.a=null}function G(a){var b=a.b;if(null==b)return null;var c=a.a=b;a.b=a.v?b.b:b.a;return c.f};function H(a){this.l=a;this.b=this.i=!1;this.f=null}function I(a){return"\n  "+a.toString().split("\n").join("\n  ")}function ub(a,b){a.i=b}function vb(a,b){a.b=b}function J(a,b){a=a.a(b);return a instanceof D?+sb(a):+a}function L(a,b){a=a.a(b);return a instanceof D?sb(a):""+a}function wb(a,b){a=a.a(b);return a instanceof D?!!a.o:!!a};function xb(a,b,c){H.call(this,a.l);this.c=a;this.j=b;this.u=c;this.i=b.i||c.i;this.b=b.b||c.b;this.c==yb&&(c.b||c.i||4==c.l||0==c.l||!b.f?b.b||b.i||4==b.l||0==b.l||!c.f||(this.f={name:c.f.name,A:b}):this.f={name:b.f.name,A:c})}m(xb,H);
function zb(a,b,c,d,e){b=b.a(d);c=c.a(d);var f;if(b instanceof D&&c instanceof D){b=F(b);for(d=G(b);d;d=G(b))for(e=F(c),f=G(e);f;f=G(e))if(a(B(d),B(f)))return!0;return!1}if(b instanceof D||c instanceof D){b instanceof D?(e=b,d=c):(e=c,d=b);f=F(e);for(var g=typeof d,k=G(f);k;k=G(f)){switch(g){case "number":k=+B(k);break;case "boolean":k=!!B(k);break;case "string":k=B(k);break;default:throw Error("Illegal primitive type for comparison.");}if(e==b&&a(k,d)||e==c&&a(d,k))return!0}return!1}return e?"boolean"==
typeof b||"boolean"==typeof c?a(!!b,!!c):"number"==typeof b||"number"==typeof c?a(+b,+c):a(b,c):a(+b,+c)}xb.prototype.a=function(a){return this.c.s(this.j,this.u,a)};xb.prototype.toString=function(){var a="Binary Expression: "+this.c;a+=I(this.j);return a+=I(this.u)};function Ab(a,b,c,d){this.L=a;this.G=b;this.l=c;this.s=d}Ab.prototype.toString=function(){return this.L};var Bb={};
function M(a,b,c,d){if(Bb.hasOwnProperty(a))throw Error("Binary operator already created: "+a);a=new Ab(a,b,c,d);return Bb[a.toString()]=a}M("div",6,1,function(a,b,c){return J(a,c)/J(b,c)});M("mod",6,1,function(a,b,c){return J(a,c)%J(b,c)});M("*",6,1,function(a,b,c){return J(a,c)*J(b,c)});M("+",5,1,function(a,b,c){return J(a,c)+J(b,c)});M("-",5,1,function(a,b,c){return J(a,c)-J(b,c)});M("<",4,2,function(a,b,c){return zb(function(d,e){return d<e},a,b,c)});
M(">",4,2,function(a,b,c){return zb(function(d,e){return d>e},a,b,c)});M("<=",4,2,function(a,b,c){return zb(function(d,e){return d<=e},a,b,c)});M(">=",4,2,function(a,b,c){return zb(function(d,e){return d>=e},a,b,c)});var yb=M("=",3,2,function(a,b,c){return zb(function(d,e){return d==e},a,b,c,!0)});M("!=",3,2,function(a,b,c){return zb(function(d,e){return d!=e},a,b,c,!0)});M("and",2,2,function(a,b,c){return wb(a,c)&&wb(b,c)});M("or",1,2,function(a,b,c){return wb(a,c)||wb(b,c)});function Cb(a,b){if(b.a.length&&4!=a.l)throw Error("Primary expression must evaluate to nodeset if filter has predicate(s).");H.call(this,a.l);this.c=a;this.j=b;this.i=a.i;this.b=a.b}m(Cb,H);Cb.prototype.a=function(a){a=this.c.a(a);return Db(this.j,a)};Cb.prototype.toString=function(){var a="Filter:"+I(this.c);return a+=I(this.j)};function Eb(a,b){if(b.length<a.F)throw Error("Function "+a.m+" expects at least"+a.F+" arguments, "+b.length+" given");if(null!==a.D&&b.length>a.D)throw Error("Function "+a.m+" expects at most "+a.D+" arguments, "+b.length+" given");a.K&&n(b,function(c,d){if(4!=c.l)throw Error("Argument "+d+" to function "+a.m+" is not of type Nodeset: "+c);});H.call(this,a.l);this.B=a;this.c=b;ub(this,a.i||qa(b,function(c){return c.i}));vb(this,a.J&&!b.length||a.I&&!!b.length||qa(b,function(c){return c.b}))}
m(Eb,H);Eb.prototype.a=function(a){return this.B.s.apply(null,sa(a,this.c))};Eb.prototype.toString=function(){var a="Function: "+this.B;if(this.c.length){var b=pa(this.c,function(c,d){return c+I(d)},"Arguments:");a+=I(b)}return a};function Fb(a,b,c,d,e,f,g,k){this.m=a;this.l=b;this.i=c;this.J=d;this.I=!1;this.s=e;this.F=f;this.D=void 0!==g?g:f;this.K=!!k}Fb.prototype.toString=function(){return this.m};var Gb={};
function N(a,b,c,d,e,f,g,k){if(Gb.hasOwnProperty(a))throw Error("Function already created: "+a+".");Gb[a]=new Fb(a,b,c,d,e,f,g,k)}N("boolean",2,!1,!1,function(a,b){return wb(b,a)},1);N("ceiling",1,!1,!1,function(a,b){return Math.ceil(J(b,a))},1);N("concat",3,!1,!1,function(a,b){return pa(ta(arguments,1),function(c,d){return c+L(d,a)},"")},2,null);N("contains",2,!1,!1,function(a,b,c){b=L(b,a);a=L(c,a);return-1!=b.indexOf(a)},2);N("count",1,!1,!1,function(a,b){return b.a(a).o},1,1,!0);
N("false",2,!1,!1,function(){return!1},0);N("floor",1,!1,!1,function(a,b){return Math.floor(J(b,a))},1);N("id",4,!1,!1,function(a,b){function c(k){if(x){var l=e.all[k];if(l){if(l.nodeType&&k==l.id)return l;if(l.length)return ra(l,function(u){return k==u.id})}return null}return e.getElementById(k)}var d=a.a,e=9==d.nodeType?d:d.ownerDocument;a=L(b,a).split(/\s+/);var f=[];n(a,function(k){k=c(k);!k||0<=ma(f,k)||f.push(k)});f.sort($a);var g=new D;n(f,function(k){g.add(k)});return g},1);
N("lang",2,!1,!1,function(){return!1},1);N("last",1,!0,!1,function(a){if(1!=arguments.length)throw Error("Function last expects ()");return a.f},0);N("local-name",3,!1,!0,function(a,b){return(a=b?rb(b.a(a)):a.a)?a.localName||a.nodeName.toLowerCase():""},0,1,!0);N("name",3,!1,!0,function(a,b){return(a=b?rb(b.a(a)):a.a)?a.nodeName.toLowerCase():""},0,1,!0);N("namespace-uri",3,!0,!1,function(){return""},0,1,!0);
N("normalize-space",3,!1,!0,function(a,b){return(b?L(b,a):B(a.a)).replace(/[\s\xa0]+/g," ").replace(/^\s+|\s+$/g,"")},0,1);N("not",2,!1,!1,function(a,b){return!wb(b,a)},1);N("number",1,!1,!0,function(a,b){return b?J(b,a):+B(a.a)},0,1);N("position",1,!0,!1,function(a){return a.b},0);N("round",1,!1,!1,function(a,b){return Math.round(J(b,a))},1);N("starts-with",2,!1,!1,function(a,b,c){b=L(b,a);a=L(c,a);return 0==b.lastIndexOf(a,0)},2);N("string",3,!1,!0,function(a,b){return b?L(b,a):B(a.a)},0,1);
N("string-length",1,!1,!0,function(a,b){return(b?L(b,a):B(a.a)).length},0,1);N("substring",3,!1,!1,function(a,b,c,d){c=J(c,a);if(isNaN(c)||Infinity==c||-Infinity==c)return"";d=d?J(d,a):Infinity;if(isNaN(d)||-Infinity===d)return"";c=Math.round(c)-1;var e=Math.max(c,0);a=L(b,a);return Infinity==d?a.substring(e):a.substring(e,c+Math.round(d))},2,3);N("substring-after",3,!1,!1,function(a,b,c){b=L(b,a);a=L(c,a);c=b.indexOf(a);return-1==c?"":b.substring(c+a.length)},2);
N("substring-before",3,!1,!1,function(a,b,c){b=L(b,a);a=L(c,a);a=b.indexOf(a);return-1==a?"":b.substring(0,a)},2);N("sum",1,!1,!1,function(a,b){a=F(b.a(a));b=0;for(var c=G(a);c;c=G(a))b+=+B(c);return b},1,1,!0);N("translate",3,!1,!1,function(a,b,c,d){b=L(b,a);c=L(c,a);var e=L(d,a);a={};for(d=0;d<c.length;d++){var f=c.charAt(d);f in a||(a[f]=e.charAt(d))}c="";for(d=0;d<b.length;d++)f=b.charAt(d),c+=f in a?a[f]:f;return c},3);N("true",2,!1,!1,function(){return!0},0);function E(a,b){this.j=a;this.c=void 0!==b?b:null;this.b=null;switch(a){case "comment":this.b=8;break;case "text":this.b=3;break;case "processing-instruction":this.b=7;break;case "node":break;default:throw Error("Unexpected argument");}}function Hb(a){return"comment"==a||"text"==a||"processing-instruction"==a||"node"==a}E.prototype.a=function(a){return null===this.b||this.b==a.nodeType};E.prototype.f=function(){return this.j};
E.prototype.toString=function(){var a="Kind Test: "+this.j;null===this.c||(a+=I(this.c));return a};function Ib(a){H.call(this,3);this.c=a.substring(1,a.length-1)}m(Ib,H);Ib.prototype.a=function(){return this.c};Ib.prototype.toString=function(){return"Literal: "+this.c};function jb(a,b){this.m=a.toLowerCase();a="*"==this.m?"*":"http://www.w3.org/1999/xhtml";this.c=b?b.toLowerCase():a}jb.prototype.a=function(a){var b=a.nodeType;if(1!=b&&2!=b)return!1;b=void 0!==a.localName?a.localName:a.nodeName;return"*"!=this.m&&this.m!=b.toLowerCase()?!1:"*"==this.c?!0:this.c==(a.namespaceURI?a.namespaceURI.toLowerCase():"http://www.w3.org/1999/xhtml")};jb.prototype.f=function(){return this.m};
jb.prototype.toString=function(){return"Name Test: "+("http://www.w3.org/1999/xhtml"==this.c?"":this.c+":")+this.m};function Jb(a){H.call(this,1);this.c=a}m(Jb,H);Jb.prototype.a=function(){return this.c};Jb.prototype.toString=function(){return"Number: "+this.c};function Kb(a,b){H.call(this,a.l);this.j=a;this.c=b;this.i=a.i;this.b=a.b;1==this.c.length&&(a=this.c[0],a.C||a.c!=Lb||(a=a.u,"*"!=a.f()&&(this.f={name:a.f(),A:null})))}m(Kb,H);function Mb(){H.call(this,4)}m(Mb,H);Mb.prototype.a=function(a){var b=new D;a=a.a;9==a.nodeType?b.add(a):b.add(a.ownerDocument);return b};Mb.prototype.toString=function(){return"Root Helper Expression"};function Nb(){H.call(this,4)}m(Nb,H);Nb.prototype.a=function(a){var b=new D;b.add(a.a);return b};Nb.prototype.toString=function(){return"Context Helper Expression"};
function Ob(a){return"/"==a||"//"==a}Kb.prototype.a=function(a){var b=this.j.a(a);if(!(b instanceof D))throw Error("Filter expression must evaluate to nodeset.");a=this.c;for(var c=0,d=a.length;c<d&&b.o;c++){var e=a[c],f=F(b,e.c.v);if(e.i||e.c!=Pb)if(e.i||e.c!=Qb){var g=G(f);for(b=e.a(new ka(g));null!=(g=G(f));)g=e.a(new ka(g)),b=pb(b,g)}else g=G(f),b=e.a(new ka(g));else{for(g=G(f);(b=G(f))&&(!g.contains||g.contains(b))&&b.compareDocumentPosition(g)&8;g=b);b=e.a(new ka(g))}}return b};
Kb.prototype.toString=function(){var a="Path Expression:"+I(this.j);if(this.c.length){var b=pa(this.c,function(c,d){return c+I(d)},"Steps:");a+=I(b)}return a};function Rb(a,b){this.a=a;this.v=!!b}
function Db(a,b,c){for(c=c||0;c<a.a.length;c++)for(var d=a.a[c],e=F(b),f=b.o,g,k=0;g=G(e);k++){var l=a.v?f-k:k+1;g=d.a(new ka(g,l,f));if("number"==typeof g)l=l==g;else if("string"==typeof g||"boolean"==typeof g)l=!!g;else if(g instanceof D)l=0<g.o;else throw Error("Predicate.evaluate returned an unexpected type.");if(!l){l=e;g=l.f;var u=l.a;if(!u)throw Error("Next must be called at least once before remove.");var K=u.b;u=u.a;K?K.a=u:g.a=u;u?u.b=K:g.b=K;g.o--;l.a=null}}return b}
Rb.prototype.toString=function(){return pa(this.a,function(a,b){return a+I(b)},"Predicates:")};function O(a,b,c,d){H.call(this,4);this.c=a;this.u=b;this.j=c||new Rb([]);this.C=!!d;b=this.j;b=0<b.a.length?b.a[0].f:null;a.M&&b&&(a=b.name,a=x?a.toLowerCase():a,this.f={name:a,A:b.A});a:{a=this.j;for(b=0;b<a.a.length;b++)if(c=a.a[b],c.i||1==c.l||0==c.l){a=!0;break a}a=!1}this.i=a}m(O,H);
O.prototype.a=function(a){var b=a.a,c=this.f,d=null,e=null,f=0;c&&(d=c.name,e=c.A?L(c.A,a):null,f=1);if(this.C)if(this.i||this.c!=Sb)if(b=F((new O(Tb,new E("node"))).a(a)),c=G(b))for(a=this.s(c,d,e,f);null!=(c=G(b));)a=pb(a,this.s(c,d,e,f));else a=new D;else a=gb(this.u,b,d,e),a=Db(this.j,a,f);else a=this.s(a.a,d,e,f);return a};O.prototype.s=function(a,b,c,d){a=this.c.B(this.u,a,b,c);return a=Db(this.j,a,d)};
O.prototype.toString=function(){var a="Step:"+I("Operator: "+(this.C?"//":"/"));this.c.m&&(a+=I("Axis: "+this.c));a+=I(this.u);if(this.j.a.length){var b=pa(this.j.a,function(c,d){return c+I(d)},"Predicates:");a+=I(b)}return a};function Ub(a,b,c,d){this.m=a;this.B=b;this.v=c;this.M=d}Ub.prototype.toString=function(){return this.m};var Vb={};function P(a,b,c,d){if(Vb.hasOwnProperty(a))throw Error("Axis already created: "+a);b=new Ub(a,b,c,!!d);return Vb[a]=b}
P("ancestor",function(a,b){for(var c=new D;b=b.parentNode;)a.a(b)&&qb(c,b);return c},!0);P("ancestor-or-self",function(a,b){var c=new D;do a.a(b)&&qb(c,b);while(b=b.parentNode);return c},!0);
var Lb=P("attribute",function(a,b){var c=new D,d=a.f();if("style"==d&&x&&b.style)return c.add(new Qa(b.style,b,"style",b.style.cssText)),c;var e=b.attributes;if(e)if(a instanceof E&&null===a.b||"*"==d)for(a=0;d=e[a];a++)x?d.nodeValue&&c.add(Sa(b,d)):c.add(d);else(d=e.getNamedItem(d))&&(x?d.nodeValue&&c.add(Sa(b,d)):c.add(d));return c},!1),Sb=P("child",function(a,b,c,d,e){return(x?mb:nb).call(null,a,b,aa(c)?c:null,aa(d)?d:null,e||new D)},!1,!0);P("descendant",gb,!1,!0);
var Tb=P("descendant-or-self",function(a,b,c,d){var e=new D;C(b,c,d)&&a.a(b)&&e.add(b);return gb(a,b,c,d,e)},!1,!0),Pb=P("following",function(a,b,c,d){var e=new D;do for(var f=b;f=f.nextSibling;)C(f,c,d)&&a.a(f)&&e.add(f),e=gb(a,f,c,d,e);while(b=b.parentNode);return e},!1,!0);P("following-sibling",function(a,b){for(var c=new D;b=b.nextSibling;)a.a(b)&&c.add(b);return c},!1);P("namespace",function(){return new D},!1);
var Wb=P("parent",function(a,b){var c=new D;if(9==b.nodeType)return c;if(2==b.nodeType)return c.add(b.ownerElement),c;b=b.parentNode;a.a(b)&&c.add(b);return c},!1),Qb=P("preceding",function(a,b,c,d){var e=new D,f=[];do f.unshift(b);while(b=b.parentNode);for(var g=1,k=f.length;g<k;g++){var l=[];for(b=f[g];b=b.previousSibling;)l.unshift(b);for(var u=0,K=l.length;u<K;u++)b=l[u],C(b,c,d)&&a.a(b)&&e.add(b),e=gb(a,b,c,d,e)}return e},!0,!0);
P("preceding-sibling",function(a,b){for(var c=new D;b=b.previousSibling;)a.a(b)&&qb(c,b);return c},!0);var Xb=P("self",function(a,b){var c=new D;a.a(b)&&c.add(b);return c},!1);function Yb(a){H.call(this,1);this.c=a;this.i=a.i;this.b=a.b}m(Yb,H);Yb.prototype.a=function(a){return-J(this.c,a)};Yb.prototype.toString=function(){return"Unary Expression: -"+I(this.c)};function Zb(a){H.call(this,4);this.c=a;ub(this,qa(this.c,function(b){return b.i}));vb(this,qa(this.c,function(b){return b.b}))}m(Zb,H);Zb.prototype.a=function(a){var b=new D;n(this.c,function(c){c=c.a(a);if(!(c instanceof D))throw Error("Path expression must evaluate to NodeSet.");b=pb(b,c)});return b};Zb.prototype.toString=function(){return pa(this.c,function(a,b){return a+I(b)},"Union Expression:")};function $b(a,b){this.a=a;this.b=b}function ac(a){for(var b,c=[];;){Q(a,"Missing right hand side of binary expression.");b=bc(a);var d=z(a.a);if(!d)break;var e=(d=Bb[d]||null)&&d.G;if(!e){a.a.a--;break}for(;c.length&&e<=c[c.length-1].G;)b=new xb(c.pop(),c.pop(),b);c.push(b,d)}for(;c.length;)b=new xb(c.pop(),c.pop(),b);return b}function Q(a,b){if(Xa(a.a))throw Error(b);}function cc(a,b){a=z(a.a);if(a!=b)throw Error("Bad token, expected: "+b+" got: "+a);}
function dc(a){a=z(a.a);if(")"!=a)throw Error("Bad token: "+a);}function ec(a){a=z(a.a);if(2>a.length)throw Error("Unclosed literal string");return new Ib(a)}
function fc(a){var b=[];if(Ob(y(a.a))){var c=z(a.a);var d=y(a.a);if("/"==c&&(Xa(a.a)||"."!=d&&".."!=d&&"@"!=d&&"*"!=d&&!/(?![0-9])[\w]/.test(d)))return new Mb;d=new Mb;Q(a,"Missing next location step.");c=gc(a,c);b.push(c)}else{a:{c=y(a.a);d=c.charAt(0);switch(d){case "$":throw Error("Variable reference not allowed in HTML XPath");case "(":z(a.a);c=ac(a);Q(a,'unclosed "("');cc(a,")");break;case '"':case "'":c=ec(a);break;default:if(isNaN(+c))if(!Hb(c)&&/(?![0-9])[\w]/.test(d)&&"("==y(a.a,1)){c=z(a.a);
c=Gb[c]||null;z(a.a);for(d=[];")"!=y(a.a);){Q(a,"Missing function argument list.");d.push(ac(a));if(","!=y(a.a))break;z(a.a)}Q(a,"Unclosed function argument list.");dc(a);c=new Eb(c,d)}else{c=null;break a}else c=new Jb(+z(a.a))}"["==y(a.a)&&(d=new Rb(hc(a)),c=new Cb(c,d))}if(c)if(Ob(y(a.a)))d=c;else return c;else c=gc(a,"/"),d=new Nb,b.push(c)}for(;Ob(y(a.a));)c=z(a.a),Q(a,"Missing next location step."),c=gc(a,c),b.push(c);return new Kb(d,b)}
function gc(a,b){if("/"!=b&&"//"!=b)throw Error('Step op should be "/" or "//"');if("."==y(a.a)){var c=new O(Xb,new E("node"));z(a.a);return c}if(".."==y(a.a))return c=new O(Wb,new E("node")),z(a.a),c;if("@"==y(a.a)){var d=Lb;z(a.a);Q(a,"Missing attribute name")}else if("::"==y(a.a,1)){if(!/(?![0-9])[\w]/.test(y(a.a).charAt(0)))throw Error("Bad token: "+z(a.a));var e=z(a.a);d=Vb[e]||null;if(!d)throw Error("No axis with name: "+e);z(a.a);Q(a,"Missing node name")}else d=Sb;e=y(a.a);if(/(?![0-9])[\w\*]/.test(e.charAt(0)))if("("==
y(a.a,1)){if(!Hb(e))throw Error("Invalid node type: "+e);e=z(a.a);if(!Hb(e))throw Error("Invalid type name: "+e);cc(a,"(");Q(a,"Bad nodetype");var f=y(a.a).charAt(0),g=null;if('"'==f||"'"==f)g=ec(a);Q(a,"Bad nodetype");dc(a);e=new E(e,g)}else if(e=z(a.a),f=e.indexOf(":"),-1==f)e=new jb(e);else{g=e.substring(0,f);if("*"==g)var k="*";else if(k=a.b(g),!k)throw Error("Namespace prefix not declared: "+g);e=e.substr(f+1);e=new jb(e,k)}else throw Error("Bad token: "+z(a.a));a=new Rb(hc(a),d.v);return c||
new O(d,e,a,"//"==b)}function hc(a){for(var b=[];"["==y(a.a);){z(a.a);Q(a,"Missing predicate expression.");var c=ac(a);b.push(c);Q(a,"Unclosed predicate expression.");cc(a,"]")}return b}function bc(a){if("-"==y(a.a))return z(a.a),new Yb(bc(a));var b=fc(a);if("|"!=y(a.a))a=b;else{for(b=[b];"|"==z(a.a);)Q(a,"Missing next union location path."),b.push(fc(a));a.a.a--;a=new Zb(b)}return a};function ic(a){switch(a.nodeType){case 1:return ia(jc,a);case 9:return ic(a.documentElement);case 11:case 10:case 6:case 12:return kc;default:return a.parentNode?ic(a.parentNode):kc}}function kc(){return null}function jc(a,b){if(a.prefix==b)return a.namespaceURI||"http://www.w3.org/1999/xhtml";var c=a.getAttributeNode("xmlns:"+b);return c&&c.specified?c.value||null:a.parentNode&&9!=a.parentNode.nodeType?jc(a.parentNode,b):null};function lc(a,b){if(!a.length)throw Error("Empty XPath expression.");a=Ua(a);if(Xa(a))throw Error("Invalid XPath expression.");b?"function"==ca(b)||(b=ha(b.lookupNamespaceURI,b)):b=function(){return null};var c=ac(new $b(a,b));if(!Xa(a))throw Error("Bad token: "+z(a));this.evaluate=function(d,e){d=c.a(new ka(d));return new T(d,e)}}
function T(a,b){if(0==b)if(a instanceof D)b=4;else if("string"==typeof a)b=2;else if("number"==typeof a)b=1;else if("boolean"==typeof a)b=3;else throw Error("Unexpected evaluation result.");if(2!=b&&1!=b&&3!=b&&!(a instanceof D))throw Error("value could not be converted to the specified type");this.resultType=b;switch(b){case 2:this.stringValue=a instanceof D?sb(a):""+a;break;case 1:this.numberValue=a instanceof D?+sb(a):+a;break;case 3:this.booleanValue=a instanceof D?0<a.o:!!a;break;case 4:case 5:case 6:case 7:var c=
F(a);var d=[];for(var e=G(c);e;e=G(c))d.push(e instanceof Qa?e.a:e);this.snapshotLength=a.o;this.invalidIteratorState=!1;break;case 8:case 9:a=rb(a);this.singleNodeValue=a instanceof Qa?a.a:a;break;default:throw Error("Unknown XPathResult type.");}var f=0;this.iterateNext=function(){if(4!=b&&5!=b)throw Error("iterateNext called with wrong result type");return f>=d.length?null:d[f++]};this.snapshotItem=function(g){if(6!=b&&7!=b)throw Error("snapshotItem called with wrong result type");return g>=d.length||
0>g?null:d[g]}}T.ANY_TYPE=0;T.NUMBER_TYPE=1;T.STRING_TYPE=2;T.BOOLEAN_TYPE=3;T.UNORDERED_NODE_ITERATOR_TYPE=4;T.ORDERED_NODE_ITERATOR_TYPE=5;T.UNORDERED_NODE_SNAPSHOT_TYPE=6;T.ORDERED_NODE_SNAPSHOT_TYPE=7;T.ANY_UNORDERED_NODE_TYPE=8;T.FIRST_ORDERED_NODE_TYPE=9;function mc(a){this.lookupNamespaceURI=ic(a)}
function nc(a,b){a=a||h;var c=a.Document&&a.Document.prototype||a.document;if(!c.evaluate||b)a.XPathResult=T,c.evaluate=function(d,e,f,g){return(new lc(d,f)).evaluate(e,g)},c.createExpression=function(d,e){return new lc(d,e)},c.createNSResolver=function(d){return new mc(d)}}ba("wgxpath.install",nc);ba("wgxpath.install",nc);var oc=window;function U(a,b){this.code=a;this.a=V[a]||pc;this.message=b||"";a=this.a.replace(/((?:^|\s+)[a-z])/g,function(c){return c.toUpperCase().replace(/^[\s\xa0]+/g,"")});b=a.length-5;if(0>b||a.indexOf("Error",b)!=b)a+="Error";this.name=a;a=Error(this.message);a.name=this.name;this.stack=a.stack||""}m(U,Error);var pc="unknown error",V={15:"element not selectable",11:"element not visible"};V[31]=pc;V[30]=pc;V[24]="invalid cookie domain";V[29]="invalid element coordinates";V[12]="invalid element state";
V[32]="invalid selector";V[51]="invalid selector";V[52]="invalid selector";V[17]="javascript error";V[405]="unsupported operation";V[34]="move target out of bounds";V[27]="no such alert";V[7]="no such element";V[8]="no such frame";V[23]="no such window";V[28]="script timeout";V[33]="session not created";V[10]="stale element reference";V[21]="timeout";V[25]="unable to set cookie";V[26]="unexpected alert open";V[13]=pc;V[9]="unknown command";var qc=za(),rc=Ba()||q("iPod"),sc=q("iPad"),tc=q("Android")&&!(Aa()||za()||q("Opera")||q("Silk")),uc=Aa(),vc=q("Safari")&&!(Aa()||q("Coast")||q("Opera")||q("Edge")||q("Edg/")||q("OPR")||za()||q("Silk")||q("Android"))&&!(Ba()||q("iPad")||q("iPod"));function wc(a){return(a=a.exec(p))?a[1]:""}var xc=function(){if(qc)return wc(/Firefox\/([0-9.]+)/);if(r||Fa||Ea)return Ka;if(uc)return Ba()||q("iPad")||q("iPod")?wc(/CriOS\/([0-9.]+)/):wc(/Chrome\/([0-9.]+)/);if(vc&&!(Ba()||q("iPad")||q("iPod")))return wc(/Version\/([0-9.]+)/);if(rc||sc){var a=/Version\/(\S+).*Mobile\/(\S+)/.exec(p);if(a)return a[1]+"."+a[2]}else if(tc)return(a=wc(/Android\s+([0-9.]+)/))?a:wc(/Version\/([0-9.]+)/);return""}();var yc=sc||rc,zc;if(tc){var Ac=/Android\s+([0-9\.]+)/.exec(p);zc=Ac?Ac[1]:"0"}else zc="0";var Bc=zc,Cc=r&&!(8<=Number(w)),Dc=r&&!(9<=Number(w));function Ec(a,b){b=b.toLowerCase();return"style"==b?Fc(a.style.cssText):Cc&&"value"==b&&Gc(a,"INPUT")?a.value:Dc&&!0===a[b]?String(a.getAttribute(b)):(a=a.getAttributeNode(b))&&a.specified?a.value:null}var Hc=/[;]+(?=(?:(?:[^"]*"){2})*[^"]*$)(?=(?:(?:[^']*'){2})*[^']*$)(?=(?:[^()]*\([^()]*\))*[^()]*$)/;
function Fc(a){var b=[];n(a.split(Hc),function(c){var d=c.indexOf(":");0<d&&(c=[c.slice(0,d),c.slice(d+1)],2==c.length&&b.push(c[0].toLowerCase(),":",c[1],";"))});b=b.join("");return b=";"==b.charAt(b.length-1)?b:b+";"}function Ic(a,b){Cc&&"value"==b&&Gc(a,"OPTION")&&null===Ec(a,"value")?(b=[],eb(a,b,!1),a=b.join("")):a=a[b];return a}function Gc(a,b){b&&"string"!==typeof b&&(b=b.toString());return!!a&&1==a.nodeType&&(!b||a.tagName.toUpperCase()==b)}
function Jc(a){return Gc(a,"OPTION")?!0:Gc(a,"INPUT")?(a=a.type.toLowerCase(),"checkbox"==a||"radio"==a):!1};function Kc(a){a=a?A(a):document;return!r||9<=Number(w)||"CSS1Compat"==(a?new fb(A(a)):la||(la=new fb)).a.compatMode?a.documentElement:a.body};var Lc=!(r&&!(r?0<=va(w,10):Oa(10))),Mc=tc?!(tc?0<=va(Bc,4):0<=va(xc,4)):!yc,Nc=r&&oc.navigator.msPointerEnabled;function Oc(a,b,c){this.a=a;this.b=b;this.f=c}Oc.prototype.create=function(a){a=A(a).createEvent("HTMLEvents");a.initEvent(this.a,this.b,this.f);return a};Oc.prototype.toString=function(){return this.a};function W(a,b,c){ja(this,a,b,c)}m(W,Oc);
W.prototype.create=function(a,b){if(!t&&this==Pc)throw new U(9,"Browser does not support a mouse pixel scroll event.");var c=A(a),d=c?c.parentWindow||c.defaultView:window;var e=c.createEvent("MouseEvents");var f=1;this==Qc&&(t||(e.wheelDelta=b.wheelDelta),t&&(f=b.wheelDelta/-40));t&&this==Pc&&(f=b.wheelDelta);e.initMouseEvent(this.a,this.b,this.f,d,f,b.clientX,b.clientY,b.clientX,b.clientY,b.ctrlKey,b.altKey,b.shiftKey,b.metaKey,b.button,b.relatedTarget);if(r&&0===e.pageX&&0===e.pageY&&Object.defineProperty){a=
Ya((a?new fb(A(a)):la||(la=new fb)).a);c=Kc(c);var g=b.clientX+a.scrollLeft-c.clientLeft,k=b.clientY+a.scrollTop-c.clientTop;Object.defineProperty(e,"pageX",{get:function(){return g}});Object.defineProperty(e,"pageY",{get:function(){return k}})}return e};function Rc(a,b,c){ja(this,a,b,c)}m(Rc,Oc);
Rc.prototype.create=function(a,b){var c=A(a);if(t&&!(r?0<=va(w,93):Oa(93))){a=c?c.parentWindow||c.defaultView:window;var d=b.charCode?0:b.keyCode;c=c.createEvent("KeyboardEvent");c.initKeyEvent(this.a,this.b,this.f,a,b.ctrlKey,b.altKey,b.shiftKey,b.metaKey,d,b.charCode);this.a==Sc&&b.preventDefault&&c.preventDefault()}else if(c=c.createEvent("Events"),c.initEvent(this.a,this.b,this.f),c.altKey=b.altKey,c.ctrlKey=b.ctrlKey,c.metaKey=b.metaKey,c.shiftKey=b.shiftKey,c.keyCode=b.charCode||b.keyCode,Ga||
Fa)c.charCode=this==Sc?c.keyCode:0;return c};function Tc(a,b,c){ja(this,a,b,c)}m(Tc,Oc);
Tc.prototype.create=function(a,b){function c(R){R=oa(R,function(v){return g.createTouch(k,a,v.identifier,v.pageX,v.pageY,v.screenX,v.screenY)});return g.createTouchList.apply(g,R)}function d(R){var v=oa(R,function(S){return{identifier:S.identifier,screenX:S.screenX,screenY:S.screenY,clientX:S.clientX,clientY:S.clientY,pageX:S.pageX,pageY:S.pageY,target:a}});v.item=function(S){return v[S]};return v}function e(R){return oa(R,function(v){return new Touch({identifier:v.identifier,screenX:v.screenX,screenY:v.screenY,
clientX:v.clientX,clientY:v.clientY,pageX:v.pageX,pageY:v.pageY,target:a})})}function f(R,v){switch(R){case 1:return d(v);case 2:return c(v);case 3:return e(v)}return null}if(!Lc)throw new U(9,"Browser does not support firing touch events.");var g=A(a),k=g?g.parentWindow||g.defaultView:window;if(Mc)var l=1;else if(TouchEvent.prototype.initTouchEvent)l=2;else if(TouchEvent&&0<TouchEvent.length)l=3;else throw new U(9,"Not able to create touch events in this browser");var u=f(l,b.changedTouches),K=b.touches==
b.changedTouches?u:f(l,b.touches),Ra=b.targetTouches==b.changedTouches?u:f(l,b.targetTouches);if(1==l)l=g.createEvent("MouseEvents"),l.initMouseEvent(this.a,this.b,this.f,k,1,0,0,b.clientX,b.clientY,b.ctrlKey,b.altKey,b.shiftKey,b.metaKey,0,b.relatedTarget),l.touches=K,l.targetTouches=Ra,l.changedTouches=u,l.scale=b.scale,l.rotation=b.rotation;else if(2==l)l=g.createEvent("TouchEvent"),0==l.initTouchEvent.length?l.initTouchEvent(K,Ra,u,this.a,k,0,0,b.clientX,b.clientY,b.ctrlKey,b.altKey,b.shiftKey,
b.metaKey):l.initTouchEvent(this.a,this.b,this.f,k,1,0,0,b.clientX,b.clientY,b.ctrlKey,b.altKey,b.shiftKey,b.metaKey,K,Ra,u,b.scale,b.rotation),l.relatedTarget=b.relatedTarget;else if(3==l)l=new TouchEvent(this.a,{touches:K,targetTouches:Ra,changedTouches:u,bubbles:this.b,cancelable:this.f,ctrlKey:b.ctrlKey,shiftKey:b.shiftKey,altKey:b.altKey,metaKey:b.metaKey});else throw new U(9,"Illegal TouchEventStrategy_ value (this is a bug)");return l};function X(a,b,c){ja(this,a,b,c)}m(X,Oc);
X.prototype.create=function(a,b){if(!Nc)throw new U(9,"Browser does not support MSGesture events.");var c=A(a);a=c?c.parentWindow||c.defaultView:window;c=c.createEvent("MSGestureEvent");c.initGestureEvent(this.a,this.b,this.f,a,1,0,0,b.clientX,b.clientY,0,0,b.translationX,b.translationY,b.scale,b.expansion,b.rotation,b.velocityX,b.velocityY,b.velocityExpansion,b.velocityAngular,(new Date).getTime(),b.relatedTarget);return c};function Y(a,b,c){ja(this,a,b,c)}m(Y,Oc);
Y.prototype.create=function(a,b){if(!Nc)throw new U(9,"Browser does not support MSPointer events.");var c=A(a);a=c?c.parentWindow||c.defaultView:window;c=c.createEvent("MSPointerEvent");c.initPointerEvent(this.a,this.b,this.f,a,0,0,0,b.clientX,b.clientY,b.ctrlKey,b.altKey,b.shiftKey,b.metaKey,b.button,b.relatedTarget,0,0,b.width,b.height,b.pressure,b.rotation,b.tiltX,b.tiltY,b.pointerId,b.pointerType,0,b.isPrimary);return c};new W("click",!0,!0);new W("contextmenu",!0,!0);new W("dblclick",!0,!0);
new W("mousedown",!0,!0);new W("mousemove",!0,!1);new W("mouseout",!0,!0);new W("mouseover",!0,!0);new W("mouseup",!0,!0);var Qc=new W(t?"DOMMouseScroll":"mousewheel",!0,!0),Pc=new W("MozMousePixelScroll",!0,!0);new Rc("keydown",!0,!0);var Sc=new Rc("keypress",!0,!0);new Rc("keyup",!0,!0);new Tc("touchend",!0,!0);new Tc("touchmove",!0,!0);new Tc("touchstart",!0,!0);new X("MSGestureChange",!0,!0);new X("MSGestureEnd",!0,!0);new X("MSGestureHold",!0,!0);new X("MSGestureStart",!0,!0);
new X("MSGestureTap",!0,!0);new X("MSInertiaStart",!0,!0);new Y("MSGotPointerCapture",!0,!1);new Y("MSLostPointerCapture",!0,!1);new Y("MSPointerCancel",!0,!0);new Y("MSPointerDown",!0,!0);new Y("MSPointerMove",!0,!0);new Y("MSPointerOver",!0,!0);new Y("MSPointerOut",!0,!0);new Y("MSPointerUp",!0,!0);function Uc(a,b){this.b={};this.a=[];this.f=0;var c=arguments.length;if(1<c){if(c%2)throw Error("Uneven number of arguments");for(var d=0;d<c;d+=2)this.set(arguments[d],arguments[d+1])}else if(a)if(a instanceof Uc)for(c=Vc(a),d=0;d<c.length;d++)this.set(c[d],a.get(c[d]));else for(d in a)this.set(d,a[d])}
function Vc(a){if(a.f!=a.a.length){for(var b=0,c=0;b<a.a.length;){var d=a.a[b];Object.prototype.hasOwnProperty.call(a.b,d)&&(a.a[c++]=d);b++}a.a.length=c}if(a.f!=a.a.length){var e={};for(c=b=0;b<a.a.length;)d=a.a[b],Object.prototype.hasOwnProperty.call(e,d)||(a.a[c++]=d,e[d]=1),b++;a.a.length=c}return a.a.concat()}Uc.prototype.get=function(a,b){return Object.prototype.hasOwnProperty.call(this.b,a)?this.b[a]:b};
Uc.prototype.set=function(a,b){Object.prototype.hasOwnProperty.call(this.b,a)||(this.f++,this.a.push(a));this.b[a]=b};var Wc={};function Z(a,b,c){da(a)&&(a=t?a.g:a.h);a=new Xc(a);!b||b in Wc&&!c||(Wc[b]={key:a,shift:!1},c&&(Wc[c]={key:a,shift:!0}));return a}function Xc(a){this.code=a}Z(8);Z(9);Z(13);var Yc=Z(16),Zc=Z(17),$c=Z(18);Z(19);Z(20);Z(27);Z(32," ");Z(33);Z(34);Z(35);Z(36);Z(37);Z(38);Z(39);Z(40);Z(44);Z(45);Z(46);Z(48,"0",")");Z(49,"1","!");Z(50,"2","@");Z(51,"3","#");Z(52,"4","$");Z(53,"5","%");Z(54,"6","^");Z(55,"7","&");Z(56,"8","*");Z(57,"9","(");Z(65,"a","A");Z(66,"b","B");Z(67,"c","C");Z(68,"d","D");
Z(69,"e","E");Z(70,"f","F");Z(71,"g","G");Z(72,"h","H");Z(73,"i","I");Z(74,"j","J");Z(75,"k","K");Z(76,"l","L");Z(77,"m","M");Z(78,"n","N");Z(79,"o","O");Z(80,"p","P");Z(81,"q","Q");Z(82,"r","R");Z(83,"s","S");Z(84,"t","T");Z(85,"u","U");Z(86,"v","V");Z(87,"w","W");Z(88,"x","X");Z(89,"y","Y");Z(90,"z","Z");var ad=Z(Ia?{g:91,h:91}:Ha?{g:224,h:91}:{g:0,h:91});Z(Ia?{g:92,h:92}:Ha?{g:224,h:93}:{g:0,h:92});Z(Ia?{g:93,h:93}:Ha?{g:0,h:0}:{g:93,h:null});Z({g:96,h:96},"0");Z({g:97,h:97},"1");
Z({g:98,h:98},"2");Z({g:99,h:99},"3");Z({g:100,h:100},"4");Z({g:101,h:101},"5");Z({g:102,h:102},"6");Z({g:103,h:103},"7");Z({g:104,h:104},"8");Z({g:105,h:105},"9");Z({g:106,h:106},"*");Z({g:107,h:107},"+");Z({g:109,h:109},"-");Z({g:110,h:110},".");Z({g:111,h:111},"/");Z(144);Z(112);Z(113);Z(114);Z(115);Z(116);Z(117);Z(118);Z(119);Z(120);Z(121);Z(122);Z(123);Z({g:107,h:187},"=","+");Z(108,",");Z({g:109,h:189},"-","_");Z(188,",","<");Z(190,".",">");Z(191,"/","?");Z(192,"`","~");Z(219,"[","{");
Z(220,"\\","|");Z(221,"]","}");Z({g:59,h:186},";",":");Z(222,"'",'"');var bd=new Uc;bd.set(1,Yc);bd.set(2,Zc);bd.set(4,$c);bd.set(8,ad);(function(a){var b=new Uc;n(Vc(a),function(c){b.set(a.get(c).code,c)});return b})(bd);var cd={"class":"className",readonly:"readOnly"},dd="allowfullscreen allowpaymentrequest allowusermedia async autofocus autoplay checked compact complete controls declare default defaultchecked defaultselected defer disabled ended formnovalidate hidden indeterminate iscontenteditable ismap itemscope loop multiple muted nohref nomodule noresize noshade novalidate nowrap open paused playsinline pubdate readonly required reversed scoped seamless seeking selected truespeed typemustmatch willvalidate".split(" ");ba("_",function(a,b){var c=null,d=b.toLowerCase();if("style"==d)return(c=a.style)&&!aa(c)&&(c=c.cssText),c;if(("selected"==d||"checked"==d)&&Jc(a)){if(!Jc(a))throw new U(15,"Element is not selectable");b="selected";c=a.type&&a.type.toLowerCase();if("checkbox"==c||"radio"==c)b="checked";return Ic(a,b)?"true":null}var e=Gc(a,"A");if(Gc(a,"IMG")&&"src"==d||e&&"href"==d)return(c=Ec(a,d))&&(c=Ic(a,d)),c;if("spellcheck"==d){c=Ec(a,d);if(null!==c){if("false"==c.toLowerCase())return"false";if("true"==c.toLowerCase())return"true"}return Ic(a,
d)+""}e=cd[b]||b;if(0<=ma(dd,d))return(c=null!==Ec(a,b)||Ic(a,e))?"true":null;try{var f=Ic(a,e)}catch(g){}null==f||da(f)?c=Ec(a,b):c=f;return null!=c?c.toString():null});; return this._.apply(null,arguments);}).apply({navigator:typeof window!='undefined'?window.navigator:null,document:typeof window!='undefined'?window.document:null}, arguments);}
