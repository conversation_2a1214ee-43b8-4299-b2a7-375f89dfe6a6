"""
Analytics module for OnlyMonster tracking data analysis
"""
import sqlite3
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from config import DATABASE_PATH, OPENROUTER_API_KEY, PRIORITY_LINKS
from link_combiner import <PERSON><PERSON><PERSON><PERSON>


class TrackingAnalytics:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.api_key = OPENROUTER_API_KEY
        self.priority_links = PRIORITY_LINKS
        self.link_combiner = LinkCombiner()
    
    def get_latest_data(self) -> List[Tuple]:
        """Get the most recent data for each tracking link with combinations applied"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, timestamp
                FROM tracking_data t1
                WHERE timestamp = (
                    SELECT MAX(timestamp)
                    FROM tracking_data t2
                    WHERE t2.tracking_link_name = t1.tracking_link_name
                )
                ORDER BY tracking_link_name
            ''')
            raw_data = cursor.fetchall()
            return self.link_combiner.combine_latest_data(raw_data)
    
    def get_previous_data(self, hours_back: int = 24) -> List[Tuple]:
        """Get data from specified hours ago with combinations applied"""
        cutoff_time = datetime.now() - timedelta(hours=hours_back)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, timestamp
                FROM tracking_data
                WHERE timestamp <= ?
                ORDER BY tracking_link_name, timestamp DESC
            ''', (cutoff_time,))

            # Get the most recent record for each link before the cutoff
            results = {}
            for name, clicks, fans, timestamp in cursor.fetchall():
                if name not in results:
                    results[name] = (name, clicks, fans, timestamp)

            raw_data = list(results.values())
            return self.link_combiner.combine_latest_data(raw_data)
    
    def calculate_changes(self, current_data: List[Tuple], previous_data: List[Tuple]) -> Dict:
        """Calculate changes between current and previous data"""
        current_dict = {name: (clicks, fans) for name, clicks, fans, _ in current_data}
        previous_dict = {name: (clicks, fans) for name, clicks, fans, _ in previous_data}
        
        changes = {
            'new_clicks': {},
            'new_fans': {},
            'conversion_rates': {},
            'no_updates': [],
            'priority_changes': {}
        }
        
        for name in current_dict:
            current_clicks, current_fans = current_dict[name]
            
            if name in previous_dict:
                prev_clicks, prev_fans = previous_dict[name]
                
                # Calculate changes
                click_change = current_clicks - prev_clicks
                fan_change = current_fans - prev_fans
                
                # Calculate conversion rates
                current_rate = (current_fans / current_clicks * 100) if current_clicks > 0 else 0
                prev_rate = (prev_fans / prev_clicks * 100) if prev_clicks > 0 else 0
                rate_change = current_rate - prev_rate
                
                changes['new_clicks'][name] = click_change
                changes['new_fans'][name] = fan_change
                changes['conversion_rates'][name] = {
                    'current': current_rate,
                    'previous': prev_rate,
                    'change': rate_change
                }
                
                # Check for no updates
                if click_change == 0 and fan_change == 0:
                    changes['no_updates'].append(name)
                
                # Track priority links
                if name in self.priority_links:
                    changes['priority_changes'][name] = {
                        'clicks_change': click_change,
                        'fans_change': fan_change,
                        'conversion_rate_change': rate_change,
                        'current_clicks': current_clicks,
                        'current_fans': current_fans,
                        'current_rate': current_rate
                    }
            else:
                # New tracking link
                changes['new_clicks'][name] = current_clicks
                changes['new_fans'][name] = current_fans
                current_rate = (current_fans / current_clicks * 100) if current_clicks > 0 else 0
                changes['conversion_rates'][name] = {
                    'current': current_rate,
                    'previous': 0,
                    'change': current_rate
                }
        
        return changes
    
    def call_openrouter_api(self, prompt: str) -> str:
        """Call OpenRouter API for AI analysis"""
        url = "https://openrouter.ai/api/v1/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "anthropic/claude-3.7-sonnet:thinking",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 1500,
            "temperature": 0.7
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            return f"AI Analysis Error: {str(e)}"
    
    def generate_analysis_prompt(self, changes: Dict) -> str:
        """Generate a prompt for AI analysis"""
        prompt = f"""
Analyze the following OnlyMonster tracking data changes and provide insights:

PRIORITY TRACKING LINKS PERFORMANCE:
{json.dumps(changes['priority_changes'], indent=2)}

ALL CHANGES SUMMARY:
- New Clicks by Source: {dict(sorted(changes['new_clicks'].items(), key=lambda x: x[1], reverse=True))}
- New Fans by Source: {dict(sorted(changes['new_fans'].items(), key=lambda x: x[1], reverse=True))}
- Links with No Updates: {changes['no_updates']}

CONVERSION RATE CHANGES:
{json.dumps({k: v for k, v in changes['conversion_rates'].items() if abs(v['change']) > 0.5}, indent=2)}

Please provide:
1. **Top Performing Sources**: Which tracking links brought in the most new fans and clicks
2. **Conversion Analysis**: Which sources have the best/worst conversion rates and significant changes
3. **Stagnant Sources**: Analysis of links with no activity
4. **Strategic Recommendations**: What actions should be taken based on this data
5. **Priority Links Focus**: Specific insights on the 7 priority tracking links

Keep the analysis concise but actionable. Focus on business insights and recommendations.
"""
        return prompt
    
    def run_analysis(self, hours_back: int = 24) -> Dict:
        """Run complete analysis comparing current vs previous data"""
        print(f"🔍 Running analysis comparing current data vs {hours_back} hours ago...")
        
        # Get data
        current_data = self.get_latest_data()
        previous_data = self.get_previous_data(hours_back)
        
        print(f"📊 Current data points: {len(current_data)}")
        print(f"📊 Previous data points: {len(previous_data)}")
        
        # Calculate changes
        changes = self.calculate_changes(current_data, previous_data)
        
        # Generate AI analysis
        print("🤖 Generating AI analysis...")
        prompt = self.generate_analysis_prompt(changes)
        ai_analysis = self.call_openrouter_api(prompt)
        
        return {
            'changes': changes,
            'ai_analysis': ai_analysis,
            'current_data': current_data,
            'previous_data': previous_data
        }
    
    def print_analysis_report(self, analysis: Dict):
        """Print a formatted analysis report"""
        changes = analysis['changes']
        
        print("\n" + "="*80)
        print("🎯 ONLYMONSTER TRACKING ANALYSIS REPORT")
        print("="*80)
        
        # Priority links summary
        print("\n🔥 PRIORITY LINKS PERFORMANCE:")
        print("-" * 50)
        for link, data in changes['priority_changes'].items():
            print(f"📈 {link}:")
            print(f"   Clicks: +{data['clicks_change']:,} (Total: {data['current_clicks']:,})")
            print(f"   Fans: +{data['fans_change']} (Total: {data['current_fans']})")
            print(f"   Conversion: {data['current_rate']:.2f}% ({data['conversion_rate_change']:+.2f}%)")
            print()
        
        # Top performers
        print("🏆 TOP CLICK SOURCES (New Clicks):")
        top_clicks = sorted(changes['new_clicks'].items(), key=lambda x: x[1], reverse=True)[:5]
        for link, clicks in top_clicks:
            if clicks > 0:
                print(f"   {link}: +{clicks:,} clicks")
        
        print("\n👥 TOP FAN SOURCES (New Fans):")
        top_fans = sorted(changes['new_fans'].items(), key=lambda x: x[1], reverse=True)[:5]
        for link, fans in top_fans:
            if fans > 0:
                print(f"   {link}: +{fans} fans")
        
        # Stagnant links
        if changes['no_updates']:
            print(f"\n⚠️  STAGNANT LINKS ({len(changes['no_updates'])} links with no updates):")
            for link in changes['no_updates']:
                print(f"   - {link}")
        
        # AI Analysis
        print("\n🤖 AI ANALYSIS & RECOMMENDATIONS:")
        print("-" * 50)
        print(analysis['ai_analysis'])
        
        print("\n" + "="*80)


if __name__ == "__main__":
    analytics = TrackingAnalytics()
    analysis = analytics.run_analysis(24)  # Compare with 24 hours ago
    analytics.print_analysis_report(analysis)
