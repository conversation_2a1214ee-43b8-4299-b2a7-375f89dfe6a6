#!/usr/bin/env python3
"""
Standardized OnlyMonster Daily Report with Historical Analysis
"""
import sqlite3
import json
from datetime import datetime, timedelta
from statistics import mean, stdev
from analytics import TrackingAnalytics
from slack_webhook import SlackWebhookNotifier
from link_combiner import <PERSON><PERSON><PERSON><PERSON>
from config import DATABASE_PATH, PRIORITY_LINKS, SLACK_BOT_TOKEN, SLACK_CHANNEL_ID

def get_historical_data():
    """Get all historical data for trend analysis with link combinations"""
    link_combiner = LinkCombiner()

    with sqlite3.connect(DATABASE_PATH) as conn:
        cursor = conn.cursor()

        # Get all data ordered by timestamp
        cursor.execute('''
            SELECT tracking_link_name, clicks, fans, timestamp
            FROM tracking_data
            ORDER BY timestamp DESC
        ''')

        data_by_link = {}
        for name, clicks, fans, timestamp in cursor.fetchall():
            if name not in data_by_link:
                data_by_link[name] = []
            data_by_link[name].append({
                'clicks': clicks,
                'fans': fans,
                'timestamp': timestamp
            })

        # Apply link combinations
        combined_data = link_combiner.combine_historical_data(data_by_link)
        return combined_data

def calculate_growth_metrics(data_by_link):
    """Calculate growth metrics and deviations"""
    metrics = {}
    
    for link in PRIORITY_LINKS:
        if link in data_by_link and len(data_by_link[link]) >= 2:
            # Sort by timestamp (most recent first)
            sorted_data = sorted(data_by_link[link], key=lambda x: x['timestamp'], reverse=True)
            
            # Current vs previous
            current = sorted_data[0]
            previous = sorted_data[1]
            
            click_growth = current['clicks'] - previous['clicks']
            fan_growth = current['fans'] - previous['fans']
            
            # Calculate historical averages if we have enough data
            if len(sorted_data) >= 3:
                # Calculate daily growth rates from all historical periods
                daily_growths = []
                for i in range(len(sorted_data) - 1):
                    curr = sorted_data[i]
                    prev = sorted_data[i + 1]
                    
                    # Calculate days between measurements
                    curr_time = datetime.fromisoformat(curr['timestamp'].replace('Z', '+00:00')) if isinstance(curr['timestamp'], str) else curr['timestamp']
                    prev_time = datetime.fromisoformat(prev['timestamp'].replace('Z', '+00:00')) if isinstance(prev['timestamp'], str) else prev['timestamp']
                    days_diff = (curr_time - prev_time).days
                    
                    if days_diff > 0:
                        daily_click_growth = (curr['clicks'] - prev['clicks']) / days_diff
                        daily_fan_growth = (curr['fans'] - prev['fans']) / days_diff
                        daily_growths.append({
                            'clicks': daily_click_growth,
                            'fans': daily_fan_growth
                        })
                
                # Calculate averages and deviations
                if daily_growths:
                    avg_daily_clicks = mean([g['clicks'] for g in daily_growths])
                    avg_daily_fans = mean([g['fans'] for g in daily_growths])
                    
                    # Current period daily rate (assuming 4 days)
                    current_daily_clicks = click_growth / 4
                    current_daily_fans = fan_growth / 4
                    
                    # Deviation from average
                    click_deviation = current_daily_clicks - avg_daily_clicks
                    fan_deviation = current_daily_fans - avg_daily_fans
                else:
                    avg_daily_clicks = avg_daily_fans = 0
                    click_deviation = fan_deviation = 0
            else:
                avg_daily_clicks = avg_daily_fans = 0
                click_deviation = fan_deviation = 0
            
            metrics[link] = {
                'current_clicks': current['clicks'],
                'current_fans': current['fans'],
                'click_growth': click_growth,
                'fan_growth': fan_growth,
                'avg_daily_clicks': avg_daily_clicks,
                'avg_daily_fans': avg_daily_fans,
                'click_deviation': click_deviation,
                'fan_deviation': fan_deviation,
                'conversion_rate': (current['fans'] / current['clicks'] * 100) if current['clicks'] > 0 else 0
            }
    
    return metrics

def generate_standardized_report():
    """Generate standardized daily report"""
    print("🎯 ONLYMONSTER STANDARDIZED DAILY REPORT")
    print("📅 Period: 6/4/25 → 6/8/25 (4 days)")
    print("="*60)
    
    # Get data and calculate metrics
    data_by_link = get_historical_data()
    metrics = calculate_growth_metrics(data_by_link)
    
    # 1. NEW SUBSCRIBERS (standardized format)
    print("\n👥 NEW SUBSCRIBERS (Where growth came from):")
    total_new_fans = 0
    total_new_clicks = 0
    
    # Sort by fan growth (descending)
    sorted_by_fans = sorted(metrics.items(), key=lambda x: x[1]['fan_growth'], reverse=True)
    
    for link, data in sorted_by_fans:
        fan_growth = data['fan_growth']
        click_growth = data['click_growth']
        total_new_fans += fan_growth
        total_new_clicks += click_growth
        
        display_name = get_display_name(link)
        if fan_growth > 0:
            print(f"   ✅ {display_name}: +{fan_growth} fans (+{click_growth} clicks)")
        elif fan_growth == 0:
            print(f"   ⚠️  {display_name}: +{fan_growth} fans (+{click_growth} clicks)")
        else:
            print(f"   ❌ {display_name}: {fan_growth} fans (+{click_growth} clicks)")
    
    # 2. TRAFFIC GROWTH (standardized format)
    print(f"\n💰 TRAFFIC GROWTH (Latest activity):")
    sorted_by_clicks = sorted(metrics.items(), key=lambda x: x[1]['click_growth'], reverse=True)
    
    for link, data in sorted_by_clicks[:5]:  # Top 5
        click_growth = data['click_growth']
        display_name = get_display_name(link)
        print(f"   📈 {display_name}: +{click_growth} clicks")
    
    # 3. STAGNANT LINKS
    print(f"\n⚠️  STAGNANT LINKS (No revenue or fans):")
    stagnant_links = [link for link, data in metrics.items() if data['fan_growth'] == 0 and data['click_growth'] == 0]
    
    if stagnant_links:
        for link in stagnant_links:
            print(f"   ❌ {link}: No activity")
    else:
        print("   ✅ All priority links showing activity!")
    
    # 4. CONVERSION RATE CHANGES
    print(f"\n📊 CONVERSION RATE CHANGES:")
    print("   Rate changes vs previous period:")

    # Calculate conversion rate changes
    conversion_changes = []
    for link, data in metrics.items():
        # Get previous conversion rate from historical data
        if link in data_by_link and len(data_by_link[link]) >= 2:
            sorted_data = sorted(data_by_link[link], key=lambda x: x['timestamp'], reverse=True)
            current = sorted_data[0]
            previous = sorted_data[1]

            current_rate = (current['fans'] / current['clicks'] * 100) if current['clicks'] > 0 else 0
            prev_rate = (previous['fans'] / previous['clicks'] * 100) if previous['clicks'] > 0 else 0
            rate_change = current_rate - prev_rate

            conversion_changes.append((link, current_rate, rate_change))

    # Sort by rate change (most improved first)
    conversion_changes.sort(key=lambda x: x[2], reverse=True)

    for link, current_rate, rate_change in conversion_changes:
        if abs(rate_change) > 0.01:  # Show significant changes
            direction = "📈" if rate_change > 0 else "📉"
            print(f"   {direction} {link}: {current_rate:.2f}% ({rate_change:+.2f}%)")
        else:
            print(f"   ➡️ {link}: {current_rate:.2f}% (stable)")

    if not conversion_changes:
        print("   ➡️ No significant conversion rate changes")
    
    # 5. DEVIATION ANALYSIS (NEW)
    print(f"\n📈 DEVIATION FROM HISTORICAL AVERAGE:")
    print("   Performance vs historical daily averages:")
    
    for link, data in metrics.items():
        click_dev = data['click_deviation']
        fan_dev = data['fan_deviation']
        
        if abs(click_dev) > 5 or abs(fan_dev) > 0.5:  # Significant deviation threshold
            click_status = "📈" if click_dev > 0 else "📉" if click_dev < 0 else "➡️"
            fan_status = "📈" if fan_dev > 0 else "📉" if fan_dev < 0 else "➡️"
            
            print(f"   {click_status} {link}:")
            print(f"      Clicks: {click_dev:+.1f}/day vs avg ({data['avg_daily_clicks']:.1f}/day)")
            print(f"      Fans: {fan_dev:+.2f}/day vs avg ({data['avg_daily_fans']:.2f}/day)")
    
    # 6. AI-POWERED IMPROVEMENT SUGGESTIONS
    print(f"\n🤖 IMPROVEMENT SUGGESTIONS:")
    print("   AI-generated recommendations:")

    # Get AI analysis for improvement suggestions
    analytics = TrackingAnalytics()

    # Prepare data for AI analysis
    ai_data = {
        'priority_changes': {},
        'conversion_rates': {},
        'stagnant_links': stagnant_links
    }

    for link, data in metrics.items():
        ai_data['priority_changes'][link] = {
            'clicks_change': data['click_growth'],
            'fans_change': data['fan_growth'],
            'current_clicks': data['current_clicks'],
            'current_fans': data['current_fans'],
            'conversion_rate': data['conversion_rate'],
            'click_deviation': data['click_deviation'],
            'fan_deviation': data['fan_deviation']
        }

        # Get conversion rate change
        if link in data_by_link and len(data_by_link[link]) >= 2:
            sorted_data = sorted(data_by_link[link], key=lambda x: x['timestamp'], reverse=True)
            current = sorted_data[0]
            previous = sorted_data[1]

            current_rate = (current['fans'] / current['clicks'] * 100) if current['clicks'] > 0 else 0
            prev_rate = (previous['fans'] / previous['clicks'] * 100) if previous['clicks'] > 0 else 0
            rate_change = current_rate - prev_rate

            ai_data['conversion_rates'][link] = {
                'current': current_rate,
                'change': rate_change
            }

    # Generate AI suggestions
    suggestions_prompt = f"""
    Based on this OnlyMonster tracking data, provide 3-5 specific, actionable improvement suggestions:

    PERFORMANCE DATA:
    {json.dumps(ai_data, indent=2)}

    IMPORTANT: When referencing tracking links, use their exact names with dashes:
    - reddit-babycheeksx
    - tiktok-aug-1-24
    - chive-nyla-aug-8
    - reels-lilfoxnaomi-aug-22
    - chive-aug-1-24
    - reels-naominoface
    - Reels2024

    Focus on:
    1. Which links to scale up (high performance)
    2. Which links need optimization (poor conversion/stagnant)
    3. Content strategy recommendations
    4. Budget allocation suggestions
    5. Platform-specific optimizations

    Keep suggestions brief, specific, and actionable. Format as bullet points with proper spacing.
    Use clear, readable text with proper spaces between words.

    Format each suggestion as:
    - [Action]: [Specific recommendation with proper spacing]

    Example format:
    - Scale reddit-babycheeksx: Increase budget by 20% due to high conversion rate
    - Optimize chive-nyla-aug-8: Test new call-to-action buttons
    """

    try:
        ai_suggestions = analytics.call_openrouter_api(suggestions_prompt)
        # Clean up and format suggestions
        suggestions_lines = ai_suggestions.split('\n')
        for line in suggestions_lines:
            line = line.strip()
            if line and (line.startswith('•') or line.startswith('-') or line.startswith('*') or any(char.isdigit() for char in line[:3])):
                # Clean up formatting but preserve spaces
                clean_line = line.replace('•', '').replace('*', '').strip()
                # Only remove leading dash if it's at the very beginning
                if clean_line.startswith('- '):
                    clean_line = clean_line[2:]
                # Remove leading numbers and dots
                if clean_line and clean_line[0].isdigit():
                    # Find the first space after numbers/dots to preserve text
                    space_index = clean_line.find(' ')
                    if space_index > 0:
                        clean_line = clean_line[space_index:].strip()

                if clean_line and len(clean_line) > 10:
                    # Fix link names by adding back dashes
                    clean_line = fix_link_names(clean_line)
                    print(f"   💡 {clean_line}")
    except Exception as e:
        print(f"   ❌ AI suggestions unavailable: {e}")
        # Fallback suggestions based on data
        print("   💡 Scale up top-performing links (Reels2024, high-conversion sources)")
        print("   💡 Optimize or pause stagnant links with 0 fan growth")
        print("   💡 Investigate successful conversion strategies for replication")

    # 7. SUMMARY (standardized format)
    print(f"\n📈 SUMMARY:")
    print(f"   Total Growth: +{total_new_fans} fans, +{total_new_clicks:,} clicks")
    print(f"   Daily Average: +{total_new_fans//4} fans, +{total_new_clicks//4:,} clicks")
    print(f"   Active Links: {len([l for l, d in metrics.items() if d['fan_growth'] > 0])}/{len(PRIORITY_LINKS)}")

    return {
        'metrics': metrics,
        'total_new_fans': total_new_fans,
        'total_new_clicks': total_new_clicks,
        'stagnant_links': stagnant_links,
        'conversion_changes': conversion_changes if 'conversion_changes' in locals() else [],
        'ai_suggestions': ai_suggestions if 'ai_suggestions' in locals() else ""
    }

def fix_link_names(text):
    """Fix link names by adding back dashes that AI removes"""
    # Common link name patterns to fix - order matters (longer patterns first)
    replacements = {
        'reelslilfoxnaomiaug22': 'reels-lilfoxnaomi-aug-22',
        'redditbabycheeksxcombined': 'reddit-babycheeksx-combined',
        'redditbabycheeksx': 'reddit-babycheeksx-combined',  # Map to combined
        'reelsnaominoface': 'reels-naominoface',
        'chivenylaaug8': 'chive-nyla-aug-8',
        'tiktokaug124': 'tiktok-aug-1-24',
        'chiveaug124': 'chive-aug-1-24',
        'Reels2024': 'Reels2024',  # This one is correct
        'babycheeksx': 'reddit-babycheeksx-combined',  # Map to combined
        # Additional patterns that might appear
        'reddit babycheeksx': 'reddit-babycheeksx-combined',
        'reddit babycheeksx combined': 'reddit-babycheeksx-combined',
        'tiktok aug 1 24': 'tiktok-aug-1-24',
        'chive nyla aug 8': 'chive-nyla-aug-8',
        'reels lilfoxnaomi aug 22': 'reels-lilfoxnaomi-aug-22',
        'chive aug 1 24': 'chive-aug-1-24',
        'reels naominoface': 'reels-naominoface',
        # Handle variations with different spacing
        'reddit-babycheeksx-combined': 'reddit-babycheeksx-combined',  # Already correct
        'tiktok-aug-1-24': 'tiktok-aug-1-24',  # Already correct
        'chive-nyla-aug-8': 'chive-nyla-aug-8',  # Already correct
        'reels-lilfoxnaomi-aug-22': 'reels-lilfoxnaomi-aug-22',  # Already correct
        'chive-aug-1-24': 'chive-aug-1-24',  # Already correct
        'reels-naominoface': 'reels-naominoface'  # Already correct
    }

    for wrong, correct in replacements.items():
        text = text.replace(wrong, correct)

    return text

def get_display_name(link_name):
    """Get display name for a link (shows individual links for combined entries)"""
    link_combiner = LinkCombiner()
    return link_combiner.get_display_name(link_name)

def format_slack_message(report_data):
    """Format visually clear message for Slack with emojis for quick scanning"""
    metrics = report_data['metrics']

    message = "🎯 *OnlyMonster Daily Report*\n"
    message += "📅 6/4/25 → 6/8/25 (4 days)\n"
    message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

    # NEW SUBSCRIBERS (with visual indicators)
    message += "👥 *NEW SUBSCRIBERS*\n"
    sorted_by_fans = sorted(metrics.items(), key=lambda x: x[1]['fan_growth'], reverse=True)

    for link, data in sorted_by_fans:
        fan_growth = data['fan_growth']
        click_growth = data['click_growth']

        if fan_growth > 0:
            message += f"✅ {link}: +{fan_growth} fans (+{click_growth} clicks)\n"
        elif fan_growth == 0:
            message += f"⚠️ {link}: +{fan_growth} fans (+{click_growth} clicks)\n"
        else:
            message += f"❌ {link}: {fan_growth} fans (+{click_growth} clicks)\n"

    # TRAFFIC GROWTH
    message += f"\n💰 *TRAFFIC GROWTH*\n"
    sorted_by_clicks = sorted(metrics.items(), key=lambda x: x[1]['click_growth'], reverse=True)

    for link, data in sorted_by_clicks[:3]:
        click_growth = data['click_growth']
        message += f"📈 {link}: +{click_growth} clicks\n"

    # CONVERSION RATE CHANGES
    message += f"\n📊 *CONVERSION RATE CHANGES*\n"
    conversion_changes = report_data.get('conversion_changes', [])

    if conversion_changes:
        # Show top 3 most significant changes
        significant_changes = [c for c in conversion_changes if abs(c[2]) > 0.01][:3]
        if significant_changes:
            for link, current_rate, rate_change in significant_changes:
                direction = "📈" if rate_change > 0 else "📉"
                message += f"{direction} {link}: {current_rate:.2f}% ({rate_change:+.2f}%)\n"
        else:
            message += "➡️ No significant conversion changes\n"
    else:
        message += "➡️ Conversion rates stable\n"

    # PERFORMANCE vs AVERAGE
    message += f"\n📊 *PERFORMANCE vs AVERAGE*\n"
    significant_deviations = []

    for link, data in metrics.items():
        click_dev = data['click_deviation']
        fan_dev = data['fan_deviation']

        if abs(click_dev) > 5 or abs(fan_dev) > 0.5:
            status = "📈" if (click_dev > 0 or fan_dev > 0) else "📉"
            significant_deviations.append(f"{status} {link}: {click_dev:+.0f} clicks/day, {fan_dev:+.1f} fans/day")

    if significant_deviations:
        for dev in significant_deviations[:3]:  # Top 3
            message += f"{dev}\n"
    else:
        message += "➡️ All links performing within normal range\n"

    # AI IMPROVEMENT SUGGESTIONS
    ai_suggestions = report_data.get('ai_suggestions', '')
    if ai_suggestions:
        message += f"\n🤖 *AI IMPROVEMENT SUGGESTIONS*\n"

        # Parse and clean AI suggestions with better formatting
        suggestions_lines = ai_suggestions.split('\n')
        suggestion_count = 0

        for line in suggestions_lines:
            line = line.strip()
            if line and (line.startswith('💡') or line.startswith('•') or line.startswith('-') or line.startswith('*') or any(char.isdigit() for char in line[:3])):
                # Clean up formatting but preserve spaces and important punctuation
                clean_line = line.replace('💡', '').replace('•', '').replace('*', '').strip()
                # Only remove leading dash if it's at the very beginning
                if clean_line.startswith('- '):
                    clean_line = clean_line[2:]
                # Remove leading numbers and dots but preserve the rest
                if clean_line and clean_line[0].isdigit():
                    # Find the first space after numbers/dots to preserve text
                    space_index = clean_line.find(' ')
                    if space_index > 0:
                        clean_line = clean_line[space_index:].strip()

                if clean_line and len(clean_line) > 15 and suggestion_count < 8:  # Limit to 8 suggestions
                    # Fix link names by adding back dashes
                    clean_line = fix_link_names(clean_line)

                    # Keep suggestions concise for Slack - max 150 chars per line
                    if len(clean_line) > 150:
                        # Find a good break point around 120 chars
                        break_point = clean_line.find(' ', 110)
                        if break_point > 0 and break_point < 160:
                            first_part = clean_line[:break_point].strip()
                            second_part = clean_line[break_point:].strip()
                            # Truncate second part if still too long
                            if len(second_part) > 100:
                                second_part = second_part[:97] + "..."
                            message += f"💡 {first_part}\n   {second_part}\n"
                        else:
                            # Just truncate if we can't find a good break
                            truncated = clean_line[:147] + "..."
                            message += f"💡 {truncated}\n"
                    else:
                        message += f"💡 {clean_line}\n"
                    suggestion_count += 1

        if suggestion_count == 0:
            message += "💡 Scale reddit-babycheeksx (highest conversion)\n"
            message += "💡 Optimize chive-nyla-aug-8 (low performance)\n"
            message += "💡 Focus budget on top conversion rates\n"

    # SUMMARY
    total_fans = report_data['total_new_fans']
    total_clicks = report_data['total_new_clicks']

    message += f"\n📈 *SUMMARY*\n"
    message += f"• +{total_fans} fans, +{total_clicks:,} clicks\n"
    message += f"• {total_fans//4} fans/day, {total_clicks//4:,} clicks/day\n"

    message += f"\n🕐 {datetime.now().strftime('%H:%M %m/%d/%y')}"

    return message

def main():
    """Main function"""
    # Generate standardized report
    report_data = generate_standardized_report()
    
    # Send to Slack
    print(f"\n📤 SENDING TO SLACK...")
    print("-" * 25)
    
    try:
        slack_notifier = SlackWebhookNotifier()
        message = format_slack_message(report_data)
        
        success = slack_notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
        
        if success:
            print("✅ Standardized report sent to Slack successfully!")
            print(f"📱 Check: https://cheeksglobal.slack.com/archives/{SLACK_CHANNEL_ID}")
        else:
            print("❌ Failed to send to Slack")
            
    except Exception as e:
        print(f"❌ Slack error: {e}")

if __name__ == "__main__":
    main()
